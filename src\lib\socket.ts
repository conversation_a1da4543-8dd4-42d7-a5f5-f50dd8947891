import { Server as NetServer } from "http";
import { NextApiRequest, NextApiResponse } from "next";
import { Server as SocketIOServer } from "socket.io";

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: SocketIOServer;
    };
  };
};

export interface GameRoom {
  id: string;
  players: Array<{
    id: string;
    name: string;
    image?: string;
    socketId: string;
  }>;
  currentTurn: number;
  gameStarted: boolean;
  buttonPresses: number;
}

export const rooms = new Map<string, GameRoom>();
