import { NextApiRequest } from "next";
import { Server as NetServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { NextApiResponseServerIO } from "@/lib/socket";

interface GameRoom {
  id: string;
  players: Array<{
    id: string;
    name: string;
    image?: string;
    socketId: string;
  }>;
  currentTurn: number;
  gameStarted: boolean;
  buttonPresses: number;
}

const rooms = new Map<string, GameRoom>();

export default (req: NextApiRequest, res: NextApiResponseServerIO) => {
  if (!res.socket.server.io) {
    console.log("Setting up Socket.IO server...");
    
    const httpServer: NetServer = res.socket.server as any;
    const io = new SocketIOServer(httpServer, {
      path: "/api/socket",
      addTrailingSlash: false,
      cors: {
        origin: process.env.NEXT_PUBLIC_BETTER_AUTH_URL || "http://localhost:3000",
        methods: ["GET", "POST"],
      },
    });

    io.on("connection", (socket) => {
      console.log("User connected:", socket.id);

      socket.on("join-game", async (data: { userId: string; userName: string; userImage?: string }) => {
        try {
          const { userId, userName, userImage } = data;
          
          // Find an available room or create a new one
          let room = Array.from(rooms.values()).find(r => r.players.length < 4 && !r.gameStarted);
          
          if (!room) {
            const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            room = {
              id: roomId,
              players: [],
              currentTurn: 0,
              gameStarted: false,
              buttonPresses: 0,
            };
            rooms.set(roomId, room);
          }

          // Check if user is already in the room
          const existingPlayer = room.players.find(p => p.id === userId);
          if (existingPlayer) {
            existingPlayer.socketId = socket.id;
          } else {
            room.players.push({
              id: userId,
              name: userName,
              image: userImage,
              socketId: socket.id,
            });
          }

          socket.join(room.id);
          (socket as any).roomId = room.id;
          (socket as any).userId = userId;

          // Start game if we have at least 2 players
          if (room.players.length >= 2 && !room.gameStarted) {
            room.gameStarted = true;
            room.currentTurn = 0;
          }

          // Emit room state to all players in the room
          io.to(room.id).emit("room-update", {
            room: {
              id: room.id,
              players: room.players.map(p => ({ id: p.id, name: p.name, image: p.image })),
              currentTurn: room.currentTurn,
              gameStarted: room.gameStarted,
              buttonPresses: room.buttonPresses,
            },
          });

          console.log(`User ${userName} joined room ${room.id}`);
        } catch (error) {
          console.error("Error joining game:", error);
          socket.emit("error", { message: "Failed to join game" });
        }
      });

      socket.on("press-button", () => {
        try {
          const roomId = (socket as any).roomId;
          const userId = (socket as any).userId;
          const room = rooms.get(roomId);

          if (!room || !room.gameStarted) {
            socket.emit("error", { message: "Game not started" });
            return;
          }

          const currentPlayer = room.players[room.currentTurn];
          if (currentPlayer.id !== userId) {
            socket.emit("error", { message: "Not your turn" });
            return;
          }

          // Increment button presses and move to next turn
          room.buttonPresses++;
          room.currentTurn = (room.currentTurn + 1) % room.players.length;

          // Emit updated room state
          io.to(room.id).emit("room-update", {
            room: {
              id: room.id,
              players: room.players.map(p => ({ id: p.id, name: p.name, image: p.image })),
              currentTurn: room.currentTurn,
              gameStarted: room.gameStarted,
              buttonPresses: room.buttonPresses,
            },
          });

          // Emit button press event
          io.to(room.id).emit("button-pressed", {
            playerId: userId,
            playerName: currentPlayer.name,
            totalPresses: room.buttonPresses,
          });

          console.log(`Button pressed by ${currentPlayer.name} in room ${room.id}`);
        } catch (error) {
          console.error("Error pressing button:", error);
          socket.emit("error", { message: "Failed to press button" });
        }
      });

      socket.on("disconnect", () => {
        try {
          const roomId = (socket as any).roomId;
          const userId = (socket as any).userId;
          
          if (roomId && userId) {
            const room = rooms.get(roomId);
            if (room) {
              // Remove player from room
              room.players = room.players.filter(p => p.id !== userId);
              
              if (room.players.length === 0) {
                // Delete empty room
                rooms.delete(roomId);
              } else {
                // Update current turn if needed
                if (room.currentTurn >= room.players.length) {
                  room.currentTurn = 0;
                }
                
                // Emit updated room state
                io.to(roomId).emit("room-update", {
                  room: {
                    id: room.id,
                    players: room.players.map(p => ({ id: p.id, name: p.name, image: p.image })),
                    currentTurn: room.currentTurn,
                    gameStarted: room.gameStarted,
                    buttonPresses: room.buttonPresses,
                  },
                });
              }
            }
          }
          
          console.log("User disconnected:", socket.id);
        } catch (error) {
          console.error("Error on disconnect:", error);
        }
      });
    });

    res.socket.server.io = io;
  }

  res.end();
};
