import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import GameClient from "@/components/game/game-client";

export default async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    redirect("/auth");
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-2">Turn-Based Game</h1>
          <p className="text-muted-foreground">
            Welcome, {session.user.name}! Join a room and take turns pressing the button.
          </p>
        </div>
        
        <GameClient 
          user={{
            id: session.user.id,
            name: session.user.name,
            image: session.user.image,
          }}
        />
      </div>
    </div>
  );
};
