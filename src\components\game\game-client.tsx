"use client";

import { useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/toast";

interface User {
  id: string;
  name: string;
  image?: string;
}

interface Player {
  id: string;
  name: string;
  image?: string;
}

interface Room {
  id: string;
  players: Player[];
  currentTurn: number;
  gameStarted: boolean;
  buttonPresses: number;
}

interface GameClientProps {
  user: User;
}

export default ({ user }: GameClientProps) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [room, setRoom] = useState<Room | null>(null);
  const [connected, setConnected] = useState(false);
  const [joining, setJoining] = useState(false);

  useEffect(() => {
    // Initialize Socket.IO server first
    fetch("/api/socket");

    const socketInstance = io(process.env.NEXT_PUBLIC_BETTER_AUTH_URL || "http://localhost:3000", {
      path: "/api/socket",
    });

    socketInstance.on("connect", () => {
      console.log("Connected to server");
      setConnected(true);
      setSocket(socketInstance);
    });

    socketInstance.on("disconnect", () => {
      console.log("Disconnected from server");
      setConnected(false);
    });

    socketInstance.on("room-update", (data: { room: Room }) => {
      console.log("Room update:", data.room);
      setRoom(data.room);
    });

    socketInstance.on("button-pressed", (data: { playerId: string; playerName: string; totalPresses: number }) => {
      toast(`${data.playerName} pressed the button! Total: ${data.totalPresses}`);
    });

    socketInstance.on("error", (data: { message: string }) => {
      toast(data.message);
    });

    return () => {
      socketInstance.disconnect();
    };
  }, []);

  const joinGame = () => {
    if (!socket || !connected) {
      toast("Not connected to server");
      return;
    }

    setJoining(true);
    socket.emit("join-game", {
      userId: user.id,
      userName: user.name,
      userImage: user.image,
    });

    // Reset joining state after a delay
    setTimeout(() => setJoining(false), 2000);
  };

  const pressButton = () => {
    if (!socket || !room) {
      toast("Not in a game room");
      return;
    }

    const currentPlayer = room.players[room.currentTurn];
    if (currentPlayer.id !== user.id) {
      toast("It's not your turn!");
      return;
    }

    socket.emit("press-button");
  };

  const getCurrentPlayerName = () => {
    if (!room || room.players.length === 0) return "";
    return room.players[room.currentTurn]?.name || "";
  };

  const isMyTurn = () => {
    if (!room || room.players.length === 0) return false;
    return room.players[room.currentTurn]?.id === user.id;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Connection Status
            <Badge variant={connected ? "default" : "destructive"}>{connected ? "Connected" : "Disconnected"}</Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Join Game */}
      {!room && (
        <Card>
          <CardHeader>
            <CardTitle>Join Game</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={joinGame} disabled={!connected || joining} size="lg" className="w-full">
              {joining ? "Joining..." : "Join Game Room"}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Game Room */}
      {room && (
        <>
          {/* Room Info */}
          <Card>
            <CardHeader>
              <CardTitle>Room: {room.id}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Players ({room.players.length}/4):</p>
                  <div className="flex flex-wrap gap-2">
                    {room.players.map((player, index) => (
                      <div
                        key={player.id}
                        className={`flex items-center gap-2 p-2 rounded-lg border ${
                          index === room.currentTurn ? "bg-primary/10 border-primary" : "bg-muted"
                        }`}>
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={player.image} />
                          <AvatarFallback>{player.name.charAt(0).toUpperCase()}</AvatarFallback>
                        </Avatar>
                        <span className="text-sm font-medium">{player.name}</span>
                        {index === room.currentTurn && (
                          <Badge variant="secondary" className="text-xs">
                            Turn
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {room.gameStarted && (
                  <div className="text-center">
                    <p className="text-lg font-semibold">Current Turn: {getCurrentPlayerName()}</p>
                    <p className="text-sm text-muted-foreground">Total Button Presses: {room.buttonPresses}</p>
                  </div>
                )}

                {!room.gameStarted && (
                  <div className="text-center">
                    <p className="text-muted-foreground">Waiting for more players to start the game...</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Game Button */}
          {room.gameStarted && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <Button
                    onClick={pressButton}
                    disabled={!isMyTurn()}
                    size="lg"
                    className="w-full h-20 text-xl font-bold"
                    variant={isMyTurn() ? "default" : "secondary"}>
                    {isMyTurn() ? "PRESS THE BUTTON!" : "Wait for your turn..."}
                  </Button>

                  {isMyTurn() && <p className="text-sm text-primary font-medium">It's your turn! Click the button above.</p>}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  );
};
